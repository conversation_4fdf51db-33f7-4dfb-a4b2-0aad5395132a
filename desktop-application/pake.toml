# 基础配置
url = "https://www.diecolor.com/sclassui/screen/entry"
name = "互动课堂"
icon = "./logo.png"
transparent = false
fullscreen = true

# 应用打包配置（对应原 --tauri.bundle 参数）
[tauri.bundle]
identifier = "com.diecolor.desktop"
copyright = "Copyright © 2025 Diecolor Inc."
shortDescription = "互动课堂大屏桌面客户端"
longDescription = "通过桌面应用更便捷地访问互动课堂"
targets = "all"

# Windows 安装程序配置
[tauri.bundle.windows]
msi = true  # 启用 MSI 安装包

# 自定义安装界面（重点！）
[tauri.bundle.windows.wix]
xml = [
  { file = "wix/custom-installer.xml", after = "Product" }
]